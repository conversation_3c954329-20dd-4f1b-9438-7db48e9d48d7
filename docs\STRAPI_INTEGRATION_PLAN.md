# 🚀 Tap2Go Strapi + Neon Integration Plan
## Professional Implementation Strategy

---

## 📋 Executive Summary

This plan outlines the strategic integration of **Strapi CMS** with **Neon PostgreSQL** into the existing Tap2Go platform, creating a hybrid architecture that leverages Firebase for real-time operations and Strapi+Neon for content management and complex data relationships.

---

## 🎯 Phase 1: Foundation & Setup (Week 1-2)

### 1.1 Environment Setup

**Neon Database Setup:**
```bash
# 1. Create Neon account and database
# 2. Get connection string  
# 3. Set up development, staging, production branches
```

**Strapi Installation:**
```bash
# Create Strapi project in separate directory
npx create-strapi-app@latest tap2go-cms --quickstart --no-run
cd tap2go-cms

# Install PostgreSQL connector
npm install pg
npm install @strapi/provider-upload-cloudinary
```

**Environment Configuration:**
```env
# .env.local (add to existing Tap2Go project)
# Strapi Configuration
STRAPI_URL=http://localhost:1337
STRAPI_API_TOKEN=your_api_token

# Neon Database
DATABASE_URL=postgresql://username:<EMAIL>/tap2go_cms
DATABASE_SSL=true

# Strapi Admin
STRAPI_ADMIN_JWT_SECRET=your_jwt_secret
STRAPI_API_TOKEN_SALT=your_api_token_salt
STRAPI_TRANSFER_TOKEN_SALT=your_transfer_token_salt
```

### 1.2 Project Structure Enhancement

**New Directory Structure:**
```
tap2go/
├── src/
│   ├── lib/
│   │   ├── strapi/           # NEW: Strapi integration
│   │   │   ├── client.ts     # Strapi API client
│   │   │   ├── types.ts      # Strapi content types
│   │   │   ├── queries.ts    # Strapi queries
│   │   │   └── cache.ts      # Strapi caching layer
│   │   ├── cms/              # NEW: CMS abstraction layer
│   │   │   ├── index.ts      # CMS interface
│   │   │   ├── strapi.ts     # Strapi implementation
│   │   │   └── types.ts      # CMS types
│   │   └── hybrid/           # NEW: Firebase + Strapi integration
│   │       ├── sync.ts       # Data synchronization
│   │       ├── resolver.ts   # Data source resolver
│   │       └── cache.ts      # Hybrid caching
│   ├── store/
│   │   ├── api/
│   │   │   ├── strapiApi.ts  # NEW: Strapi RTK Query
│   │   │   └── hybridApi.ts  # NEW: Hybrid data API
│   └── types/
│       ├── strapi.ts         # NEW: Strapi types
│       └── cms.ts            # NEW: CMS types
├── tap2go-cms/               # NEW: Separate Strapi project
│   ├── config/
│   ├── src/
│   │   ├── api/
│   │   └── components/
│   └── package.json
└── docs/
    ├── STRAPI_INTEGRATION.md # NEW: Integration docs
    └── CMS_ARCHITECTURE.md   # NEW: CMS architecture
```

---

## 🏗️ Phase 2: Content Type Design (Week 2-3)

### 2.1 Strapi Content Types

**Restaurant Content Management:**
```typescript
// Restaurant Content Type (Strapi)
interface StrapiRestaurant {
  id: number;
  name: string;
  slug: string;
  description: string;
  longDescription: string;
  images: Media[];
  coverImage: Media;
  gallery: Media[];
  cuisine: Cuisine[];
  features: Feature[];
  story: string;
  awards: Award[];
  certifications: Certification[];
  socialMedia: SocialMedia;
  seo: SEO;
  publishedAt: string;
  createdAt: string;
  updatedAt: string;
}
```

**Menu Management:**
```typescript
// Menu Category (Strapi)
interface MenuCategory {
  id: number;
  name: string;
  description: string;
  image: Media;
  sortOrder: number;
  isActive: boolean;
  restaurant: StrapiRestaurant;
  menuItems: MenuItem[];
}

// Menu Item (Strapi)
interface MenuItem {
  id: number;
  name: string;
  description: string;
  shortDescription: string;
  images: Media[];
  price: number;
  originalPrice?: number;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  spiceLevel: 'mild' | 'medium' | 'hot' | 'extra-hot';
  allergens: Allergen[];
  nutritionalInfo: NutritionalInfo;
  ingredients: Ingredient[];
  modifierGroups: ModifierGroup[];
  tags: Tag[];
  isAvailable: boolean;
  preparationTime: number;
  category: MenuCategory;
  seo: SEO;
}
```

**Promotional Content:**
```typescript
// Promotion (Strapi)
interface Promotion {
  id: number;
  title: string;
  description: string;
  image: Media;
  bannerImage: Media;
  type: 'discount' | 'bogo' | 'free-delivery' | 'cashback';
  discountType: 'percentage' | 'fixed' | 'free-item';
  discountValue: number;
  minimumOrderValue?: number;
  validFrom: string;
  validUntil: string;
  isActive: boolean;
  restaurants: StrapiRestaurant[];
  categories: MenuCategory[];
  menuItems: MenuItem[];
  maxUsagePerUser?: number;
  totalUsageLimit?: number;
  promoCode?: string;
  terms: string;
  seo: SEO;
}
```

### 2.2 Data Architecture Mapping

**Firebase (Real-time) ↔ Strapi (Content):**
```typescript
// Data Source Mapping
interface DataSourceMap {
  // Firebase Collections (Real-time operations)
  firebase: {
    users: 'Authentication & user profiles';
    orders: 'Order processing & tracking';
    drivers: 'Driver location & status';
    notifications: 'Real-time notifications';
    analytics: 'Real-time analytics events';
    payments: 'Payment processing';
    reviews: 'User reviews & ratings';
  };
  
  // Strapi Collections (Content management)
  strapi: {
    restaurants: 'Restaurant content & branding';
    menuCategories: 'Menu organization';
    menuItems: 'Menu content & details';
    promotions: 'Marketing campaigns';
    blogPosts: 'Content marketing';
    pages: 'Static page content';
    faqs: 'Help & support content';
    policies: 'Legal documents';
    banners: 'Homepage banners';
    features: 'Platform features';
  };
}
```

---

## 🔧 Phase 3: Integration Layer Development (Week 3-4)

### 3.1 Strapi Client Setup

**Create Strapi API Client:**
```typescript
// src/lib/strapi/client.ts
import axios from 'axios';

export class StrapiClient {
  private baseURL: string;
  private apiToken: string;
  
  constructor() {
    this.baseURL = process.env.STRAPI_URL || 'http://localhost:1337';
    this.apiToken = process.env.STRAPI_API_TOKEN || '';
  }
  
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const response = await axios.get(`${this.baseURL}/api${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      },
      params,
    });
    return response.data;
  }
  
  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await axios.post(`${this.baseURL}/api${endpoint}`, data, {
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  }
}

export const strapiClient = new StrapiClient();
```

### 3.2 Hybrid Data Resolver

**Create Data Source Resolver:**
```typescript
// src/lib/hybrid/resolver.ts
import { strapiClient } from '../strapi/client';
import { db } from '../firebase';
import { collection, getDocs, query, where } from 'firebase/firestore';

export class HybridDataResolver {
  async getRestaurantData(restaurantId: string) {
    // Get operational data from Firebase
    const firebaseData = await this.getFirebaseRestaurant(restaurantId);
    
    // Get content data from Strapi
    const strapiData = await this.getStrapiRestaurant(restaurantId);
    
    // Merge data sources
    return {
      ...firebaseData,
      content: strapiData,
      source: 'hybrid'
    };
  }
  
  private async getFirebaseRestaurant(id: string) {
    // Firebase restaurant operational data
    const restaurantRef = collection(db, 'restaurants');
    const q = query(restaurantRef, where('id', '==', id));
    const snapshot = await getDocs(q);
    return snapshot.docs[0]?.data();
  }
  
  private async getStrapiRestaurant(firebaseId: string) {
    // Strapi restaurant content data
    return await strapiClient.get(`/restaurants`, {
      filters: { firebaseId: { $eq: firebaseId } },
      populate: ['images', 'gallery', 'cuisine', 'features']
    });
  }
}
```

---

## 🔄 Phase 4: Redux Integration (Week 4-5)

### 4.1 Strapi RTK Query Setup

**Create Strapi API Slice:**
```typescript
// src/store/api/strapiApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const strapiBaseQuery = fetchBaseQuery({
  baseUrl: `${process.env.STRAPI_URL}/api`,
  prepareHeaders: (headers) => {
    headers.set('authorization', `Bearer ${process.env.STRAPI_API_TOKEN}`);
    return headers;
  },
});

export const strapiApi = createApi({
  reducerPath: 'strapiApi',
  baseQuery: strapiBaseQuery,
  tagTypes: [
    'Restaurant',
    'MenuCategory',
    'MenuItem',
    'Promotion',
    'BlogPost',
    'Page',
    'Banner'
  ],
  endpoints: (builder) => ({
    // Restaurant Content
    getRestaurantContent: builder.query({
      query: (id) => `/restaurants/${id}?populate=*`,
      providesTags: ['Restaurant'],
    }),

    // Menu Management
    getMenuCategories: builder.query({
      query: (restaurantId) => `/menu-categories?filters[restaurant][id][$eq]=${restaurantId}&populate=*`,
      providesTags: ['MenuCategory'],
    }),

    getMenuItems: builder.query({
      query: (categoryId) => `/menu-items?filters[category][id][$eq]=${categoryId}&populate=*`,
      providesTags: ['MenuItem'],
    }),

    // Promotions
    getActivePromotions: builder.query({
      query: () => `/promotions?filters[isActive][$eq]=true&populate=*`,
      providesTags: ['Promotion'],
    }),

    // Content Pages
    getPageContent: builder.query({
      query: (slug) => `/pages?filters[slug][$eq]=${slug}&populate=*`,
      providesTags: ['Page'],
    }),
  }),
});

export const {
  useGetRestaurantContentQuery,
  useGetMenuCategoriesQuery,
  useGetMenuItemsQuery,
  useGetActivePromotionsQuery,
  useGetPageContentQuery,
} = strapiApi;
```

### 4.2 Store Configuration Update

**Add Strapi API to Store:**
```typescript
// src/store/index.ts (Enhanced)
import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from './api/apiSlice';
import { strapiApi } from './api/strapiApi';
import { hybridApi } from './api/hybridApi';

export const store = configureStore({
  reducer: {
    // Existing reducers
    auth: authSlice.reducer,
    ui: uiSlice.reducer,
    cart: cartSlice.reducer,
    orders: ordersSlice.reducer,
    restaurants: restaurantsSlice.reducer,

    // API slices
    [apiSlice.reducerPath]: apiSlice.reducer,
    [strapiApi.reducerPath]: strapiApi.reducer,
    [hybridApi.reducerPath]: hybridApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    })
    .concat(apiSlice.middleware)
    .concat(strapiApi.middleware)
    .concat(hybridApi.middleware),
});
```
